# OpenAI 模型获取功能使用示例

## 📱 用户界面演示

### 1. 创建OpenAI提供商

当用户在提供商编辑界面选择"OpenAI"类型时：

```
┌─────────────────────────────────────┐
│ 添加提供商                           │
├─────────────────────────────────────┤
│ 基本信息                             │
│ ┌─────────────────────────────────┐ │
│ │ 名称: My OpenAI Provider        │ │
│ └─────────────────────────────────┘ │
│ ┌─────────────────────────────────┐ │
│ │ 类型: OpenAI ▼                  │ │
│ └─────────────────────────────────┘ │
│                                     │
│ API 配置                            │
│ ┌─────────────────────────────────┐ │
│ │ API Key: sk-xxx...              │ │
│ └─────────────────────────────────┘ │
│ ┌─────────────────────────────────┐ │
│ │ Base URL: (可选)                │ │
│ └─────────────────────────────────┘ │
│                                     │
│ 模型配置                             │
│ ┌─────────────────────────────────┐ │
│ │ 支持的模型                       │ │
│ │ [获取模型] [添加]                │ │
│ │                                 │ │
│ │ 暂无模型                         │ │
│ │ 点击"添加"按钮手动添加模型，      │ │
│ │ 或点击"获取模型"从API获取         │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

### 2. 点击"获取模型"按钮

用户点击"获取模型"按钮后，界面显示加载状态：

```
┌─────────────────────────────────────┐
│ 模型配置                             │
│ ┌─────────────────────────────────┐ │
│ │ 支持的模型                       │ │
│ │ [⟳ 获取模型] [添加]              │ │
│ │                                 │ │
│ │ 正在从API获取模型列表...          │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

### 3. 模型选择对话框

API调用成功后，显示模型选择对话框：

```
┌─────────────────────────────────────┐
│ 选择要添加的模型                     │
├─────────────────────────────────────┤
│ ┌─────────────────────────────────┐ │
│ │ ☐ GPT-4o                        │ │
│ │   gpt-4o                        │ │
│ │                                 │ │
│ │ ☐ GPT-4o Mini                   │ │
│ │   gpt-4o-mini                   │ │
│ │                                 │ │
│ │ ☐ GPT-4 Turbo                   │ │
│ │   gpt-4-turbo                   │ │
│ │                                 │ │
│ │ ☐ GPT-4                         │ │
│ │   gpt-4                         │ │
│ │                                 │ │
│ │ ☐ GPT-3.5 Turbo                 │ │
│ │   gpt-3.5-turbo                 │ │
│ └─────────────────────────────────┘ │
│                                     │
│                          [关闭]     │
└─────────────────────────────────────┘
```

### 4. 添加模型后的界面

用户选择模型后，界面更新显示已添加的模型：

```
┌─────────────────────────────────────┐
│ 模型配置                             │
│ ┌─────────────────────────────────┐ │
│ │ 支持的模型                       │ │
│ │ [获取模型] [添加]                │ │
│ │                                 │ │
│ │ ┌─────────────────────────────┐ │ │
│ │ │ GPT-4o                      │ │ │
│ │ │ 模型ID: gpt-4o              │ │ │
│ │ │ 功能: 聊天, 图片分析         │ │ │
│ │ │                    [✏️] [🗑️] │ │ │
│ │ └─────────────────────────────┘ │ │
│ │                                 │ │
│ │ ┌─────────────────────────────┐ │ │
│ │ │ GPT-3.5 Turbo               │ │ │
│ │ │ 模型ID: gpt-3.5-turbo       │ │ │
│ │ │ 功能: 聊天                   │ │ │
│ │ │                    [✏️] [🗑️] │ │ │
│ │ └─────────────────────────────┘ │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

## 🔧 技术流程

### API调用流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant UI as 界面
    participant API as OpenAI API
    
    U->>UI: 点击"获取模型"
    UI->>UI: 显示加载状态
    UI->>API: 调用 listModels()
    API-->>UI: 返回模型列表
    UI->>UI: 过滤聊天模型
    UI->>UI: 显示选择对话框
    U->>UI: 选择模型
    UI->>UI: 添加到模型列表
    UI->>U: 更新界面
```

### 错误处理流程

```mermaid
flowchart TD
    A[点击获取模型] --> B[验证API Key]
    B --> C{API Key有效?}
    C -->|否| D[显示错误: 无效API Key]
    C -->|是| E[调用API]
    E --> F{API调用成功?}
    F -->|否| G[显示错误信息]
    F -->|是| H[过滤模型]
    H --> I[显示选择对话框]
    G --> J[结束]
    D --> J
    I --> J
```

## 🎯 使用场景

### 场景1: 新用户首次配置

```
用户操作:
1. 创建新的OpenAI提供商
2. 输入API Key
3. 点击"获取模型"
4. 从列表中选择需要的模型
5. 保存配置

结果:
- 自动获取最新的可用模型
- 避免手动输入模型名称的错误
- 确保模型名称的准确性
```

### 场景2: 更新现有提供商

```
用户操作:
1. 编辑现有OpenAI提供商
2. 点击"获取模型"查看新模型
3. 添加新发布的模型
4. 保存更新

结果:
- 及时获取OpenAI新发布的模型
- 保持模型列表的最新状态
```

### 场景3: 使用代理服务器

```
用户操作:
1. 设置自定义Base URL
2. 输入代理服务器的API Key
3. 点击"获取模型"
4. 验证代理服务器的可用模型

结果:
- 验证代理服务器配置正确
- 获取代理服务器支持的模型列表
```

## ⚠️ 注意事项

### 网络要求
- 需要稳定的网络连接
- 能够访问OpenAI API或指定的代理服务器
- 防火墙允许HTTPS连接

### API限制
- 遵守OpenAI的API调用频率限制
- 确保API Key有足够的权限
- 注意API调用可能产生的费用

### 数据安全
- API Key在传输过程中加密
- 不会在本地存储敏感信息
- 遵循最佳安全实践
