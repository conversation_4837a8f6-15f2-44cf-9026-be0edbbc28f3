import 'package:flutter/material.dart';
import 'package:openai_dart/openai_dart.dart' as oai; // 使用别名避免与本地 AiModel 冲突
import '../models/ai_model.dart';
import '../services/notification_service.dart'; // 确保路径正确
import 'package:dio/dio.dart';

class OpenAIModelManagementDialog extends StatefulWidget {
  final String apiKey;
  final String baseUrl;
  final List<AiModel> initialSelectedModels;

  const OpenAIModelManagementDialog({
    super.key,
    required this.apiKey,
    required this.baseUrl,
    required this.initialSelectedModels,
  });

  @override
  State<OpenAIModelManagementDialog> createState() =>
      _OpenAIModelManagementDialogState();
}

class _OpenAIModelManagementDialogState
    extends State<OpenAIModelManagementDialog> {
  bool _isLoadingApi = false;
  List<oai.Model> _apiModels = [];
  Set<String> _selectedModelIds = {};
  List<oai.Model> _filteredApiModels = [];
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _selectedModelIds = widget.initialSelectedModels
        .map((m) => m.name)
        .toSet();
    _fetchModelsFromApi();
    _searchController.addListener(_updateFilteredModels);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _fetchModelsFromApi() async {
    setState(() => _isLoadingApi = true);
    try {
      String url = widget.baseUrl;
      if (url.endsWith('/')) {
        url = url.substring(0, url.length - 1);
      }
      url += '/models';

      final dio = Dio();
      final response = await dio.get(
        url,
        options: Options(
          headers: {
            'Authorization': 'Bearer ${widget.apiKey}',
          },
        ),
      );

      if (mounted) {
        if (response.data != null &&
            response.data is Map &&
            response.data['data'] is List) {
          final List<dynamic> modelsData = response.data['data'];
          setState(() {
            _apiModels = modelsData.map((modelMap) {
              if (modelMap is Map<String, dynamic>) {
                return oai.Model(
                  id: modelMap['id'] as String,
                  created: modelMap['created'] is int
                      ? modelMap['created'] as int
                      : (modelMap['created'] is String
                          ? (int.tryParse(modelMap['created'] as String) ?? DateTime.now().millisecondsSinceEpoch ~/ 1000)
                          : DateTime.now().millisecondsSinceEpoch ~/ 1000),
                  object: oai.ModelObject.model, // 修正: 使用枚举值
                  ownedBy: modelMap['owned_by'] as String? ?? 'unknown',
                );
              } else {
                throw FormatException('模型数据项格式不正确: $modelMap');
              }
            }).toList();
            _updateFilteredModels();
          });
        } else {
          NotificationService().showError('获取模型列表失败: API响应数据格式不正确或数据为空');
          _apiModels = [];
          _updateFilteredModels();
        }
      }
    } on DioException catch (e) {
      if (mounted) {
        String errorMessage = '获取模型列表失败 (Dio): ${e.message ?? "未知错误"}';
        if (e.response != null) {
          errorMessage += '\n状态码: ${e.response?.statusCode}';
          if (e.response?.data != null) {
            String responseDataStr = e.response!.data.toString();
            errorMessage += '\n响应: ${responseDataStr.substring(0, responseDataStr.length > 200 ? 200 : responseDataStr.length)}${responseDataStr.length > 200 ? "..." : ""}';
          }
        }
        NotificationService().showError(errorMessage);
        _apiModels = [];
        _updateFilteredModels();
      }
    } catch (e) {
      if (mounted) {
        NotificationService().showError('获取模型列表失败 (其他): $e');
        _apiModels = [];
        _updateFilteredModels();
      }
    } finally {
      if (mounted) {
        setState(() => _isLoadingApi = false);
      }
    }
  }

  void _updateFilteredModels() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredApiModels = _apiModels.where((model) {
        return model.id.toLowerCase().contains(query);
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('管理 OpenAI 模型'),
      content: SizedBox(
        width: double.maxFinite,
        height: MediaQuery.of(context).size.height * 0.6,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                labelText: '搜索模型ID',
                hintText: '例如 gpt-4',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            if (_isLoadingApi)
              const Expanded(child: Center(child: CircularProgressIndicator()))
            else if (_filteredApiModels.isEmpty &&
                _searchController.text.isNotEmpty)
              const Expanded(child: Center(child: Text('未找到匹配的模型')))
            else if (_apiModels.isEmpty)
              const Expanded(child: Center(child: Text('未能从API获取到模型列表，或列表为空')))
            else
              Expanded(
                child: ListView.builder(
                  itemCount: _filteredApiModels.length,
                  itemBuilder: (context, index) {
                    final apiModel = _filteredApiModels[index];
                    final bool isSelected = _selectedModelIds.contains(
                      apiModel.id,
                    );
                    return CheckboxListTile(
                      title: Text(apiModel.id),
                      value: isSelected,
                      onChanged: (bool? value) {
                        setState(() {
                          if (value == true) {
                            _selectedModelIds.add(apiModel.id);
                          } else {
                            _selectedModelIds.remove(apiModel.id);
                          }
                        });
                      },
                    );
                  },
                ),
              ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        FilledButton(
          onPressed: () {
            final now = DateTime.now();
            final List<AiModel> resultModels = _selectedModelIds.map((id) {
              final existingModel = widget.initialSelectedModels.firstWhere(
                (m) => m.name == id,
                orElse: () => AiModel(
                  id: id,
                  name: id,
                  displayName: id,
                  createdAt: now,
                  updatedAt: now,
                ),
              );
              return existingModel.copyWith(updatedAt: now);
            }).toList();
            Navigator.of(context).pop(resultModels);
          },
          child: const Text('确定'),
        ),
      ],
    );
  }
}
